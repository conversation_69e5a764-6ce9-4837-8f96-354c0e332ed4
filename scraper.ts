// src/crawler.ts

import axios from "axios";
import * as cheerio from "cheerio";
import * as fs from "fs";
import * as path from "path";

// --- DATA STRUCTURE DEFINITION ---
interface ScrapedPage {
  url: string;
  title: string;
  scrapedTimestamp: string;
  route: string;
  contentBlocks: ContentBlock[];
  links: {
    internal: Link[];
    external: Link[];
    files: Link[];
  };
}

interface ContentBlock {
  type: "h1" | "h2" | "h3" | "p" | "image";
  text?: string;
  src?: string;
  alt?: string;
}

interface Link {
  text: string;
  href: string;
}

// --- CORE SCRAPING FUNCTION ---
const scrapePage = async (url: string): Promise<ScrapedPage | null> => {
  const baseUrl = new URL(url).origin;

  try {
    console.log(`Scraping: ${url}`);
    const { data: html } = await axios.get<string>(url, { timeout: 10000 });
    const $ = cheerio.load(html);

    // --- DATA EXTRACTION ---

    // Exclude header, nav, and footer from content selection for cleaner results
    $("header, nav, footer").remove();

    const title =
      $("head title").text().trim() || $("h1").first().text().trim();
    const contentBlocks: ContentBlock[] = [];

    // Select all relevant content tags from the main body
    $("body h1, body h2, body h3, body p, body img").each((_, element) => {
      const el = $(element);
      const tagName = el.prop("tagName")?.toLowerCase();

      switch (tagName) {
        case "h1":
        case "h2":
        case "h3":
        case "p":
          const text = el.text().trim();
          if (text) {
            // Only add if there is text content
            contentBlocks.push({ type: tagName, text });
          }
          break;
        case "img":
          const src = el.attr("src");
          if (src) {
            contentBlocks.push({
              type: "image",
              src: new URL(src, baseUrl).href, // Resolve relative URLs
              alt: el.attr("alt")?.trim() || "",
            });
          }
          break;
      }
    });

    // --- LINK EXTRACTION ---
    // We need to re-load the HTML to get links from nav/footer that we removed
    const $withAllContent = cheerio.load(html);
    const internalLinks: Link[] = [];
    const externalLinks: Link[] = [];
    const fileLinks: Link[] = [];
    const discoveredUrls = new Set<string>();

    $withAllContent("a").each((i, element) => {
      const link = $(element);
      const href = link.attr("href");
      const text = link.text().trim();

      if (href && !href.startsWith("mailto:") && !href.startsWith("tel:")) {
        try {
          const absoluteUrl = new URL(href, baseUrl);

          // Categorize the link
          if (/\.(pdf|docx|jpg|png|zip)$/i.test(absoluteUrl.pathname)) {
            fileLinks.push({ text, href: absoluteUrl.href });
          } else if (absoluteUrl.hostname === new URL(baseUrl).hostname) {
            internalLinks.push({ text, href: absoluteUrl.href });
            // Add to the list of URLs to crawl next
            discoveredUrls.add(absoluteUrl.href);
          } else {
            externalLinks.push({ text, href: absoluteUrl.href });
          }
        } catch (error) {
          console.warn(`Ignoring invalid link: ${href}`);
        }
      }
    });

    // --- FINAL JSON STRUCTURE ---
    const pageData: ScrapedPage = {
      url,
      title,
      scrapedTimestamp: new Date().toISOString(),
      route: new URL(url).pathname,
      contentBlocks,
      links: {
        internal: internalLinks,
        external: externalLinks,
        files: fileLinks,
      },
    };

    return pageData;
  } catch (error: any) {
    if (error.isAxiosError) {
      console.error(`Axios error scraping ${url}: ${error.message}`);
    } else {
      console.error(`Failed to scrape ${url}:`, error);
    }
    return null;
  }
};

// --- CRAWLER LOGIC ---
const main = async () => {
  const startUrl = "https://www.gymtrhovesviny.cz/";
  const queue: string[] = [startUrl];
  const visited = new Set<string>();
  const outputDir = "output";

  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir);
  }

  while (queue.length > 0) {
    const currentUrl = queue.shift()!;
    if (visited.has(currentUrl)) {
      continue;
    }

    visited.add(currentUrl);
    const pageData = await scrapePage(currentUrl);

    if (pageData) {
      // Save the scraped data to a file
      const routePath = pageData.route.replace(/\/$/, ""); // remove trailing slash
      const filename = (routePath.replace(/\//g, "_") || "index") + ".json";
      fs.writeFileSync(
        path.join(outputDir, filename),
        JSON.stringify(pageData, null, 2)
      );

      // Add newly discovered internal links to the queue
      pageData.links.internal.forEach((link) => {
        if (!visited.has(link.href)) {
          queue.push(link.href);
        }
      });
    }

    // Add a small delay to be polite to the server
    await new Promise((resolve) => setTimeout(resolve, 50));
  }

  console.log("Crawling complete. All discovered pages have been scraped.");
};

main();
